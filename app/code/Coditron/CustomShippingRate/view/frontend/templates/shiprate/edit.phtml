<?php
/** @var \Coditron\CustomShippingRate\Block\TableRates $block */
$helper = $block->getMpHelper();
$isPartner = $helper->isSeller();
$backUrl = $block->getStoreUrl().'coditron_customshippingrate/shiptablerates/manage/';

$sellerId = $block->getSellerId();
$shipRate = $block->getShipRate();

$weightUnits = $block->getStoreWeightUnits();
$countriesListHtml = $block->getCountries(true, $shipRate->getCountries());
$serviceTypeOptions = $block->getServiceTypeOptions();
$returnAddressOptions = $block->getReturnAddressOptions();
?>
<div class="wk-mpsellercategory-container">
    <?php if ($isPartner == 1): ?>
        <?php $isThreshold = $block->getRequest()->getParam('threshold', false); ?>
        <form action="<?= $escaper->escapeUrl($block->getUrl('coditron_customshippingrate/shiptablerates/save', ['threshold' => $isThreshold ? 1 : 0])) ?>"
        enctype="multipart/form-data" method="post" id="form-save-ship-rates"
        data-mage-init='{"validation":{}}'>
            <div class="fieldset wk-ui-component-container">
                <?= $block->getBlockHtml('formkey') ?>
                <?= $block->getBlockHtml('seller.formkey') ?>
                <input type="hidden" name="id" value="<?= $escaper->escapeHtml($shipRate->getShiptableratesId()) ?>">
                <input type="hidden" name="threshold" value="<?= $isThreshold ? 1 : 0 ?>">
                <input type="hidden" name="seller_id" value="<?= $escaper->escapeHtml($sellerId) ?>">
                <div class="page-main-actions">
                    <div class="page-actions-placeholder"></div>
                    <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
                        <div class="page-actions-inner" data-title="<?= $escaper->escapeHtml(__("Ship Methods")); ?>">
                            <div class="page-actions-buttons">
                                <button id="back" title="<?= $escaper->escapeHtml(__("Back")); ?>" type="button"
                                class="action- scalable back wk-ui-grid-btn-back wk-ui-grid-btn"
                                data-ui-id="back-button">
                                    <span><?= $escaper->escapeHtml(__("Back")); ?></span>
                                </button>
                                <button id="save"
                                title="<?= $escaper->escapeHtml(__("Save Shipping Method")); ?>" type="submit"
                                class="action- scalable save primary ui-button ui-widget
                                ui-state-default ui-corner-all ui-button-text-only wk-ui-grid-btn
                                wk-ui-grid-btn-primary"
                                data-form-role="save"
                                data-ui-id="save-button" role="button" aria-disabled="false">
                                    <span class="ui-button-text">
                                        <span><?= $escaper->escapeHtml(__("Save Shipping Method")); ?></span>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php if (!$isThreshold): ?>
                <div class="field required">
                    <label for="courier_name" class="label">
                        <span><?= $escaper->escapeHtml(__("Carrier Name")); ?></span>
                    </label>
                    <div class="tooltip">
                     <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                     <span class="tooltiptext"><?= $escaper->escapeHtml(__('Your carrier name')) ?></span>
                     </div>
                    <div class="control">
                        <input type="text" class="input-text required-entry" name="courier_name"
                        data-validate="{required:true}" title="<?= $escaper->escapeHtml(__("Carrier Name")); ?>"
                        id="courier_name" value="<?= $escaper->escapeHtml($block->escapeHtml($shipRate->getCourierName())) ?>">
                    </div>
                </div>
                <?php endif; ?>

                <?php if ($isThreshold): ?>
                <div class="field required">
                    <label for="min_amount" class="label">
                        <span><?= $escaper->escapeHtml(__("Minimum Amount for Free Shipping (USD)")); ?></span>
                    </label>
                    <div class="tooltip">
                        <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                        <span class="tooltiptext"><?= $escaper->escapeHtml(__('Minimum order amount required for free shipping')) ?></span>
                    </div>
                    <div class="control">
                        <input type="text" class="input-text required-entry validate-number validate-greater-than-zero" name="min_amount"
                        data-validate="{required:true, 'validate-number':true, 'validate-greater-than-zero':true}"
                        title="<?= $escaper->escapeHtml(__("minimum amount")); ?>"
                        id="min_amount" value="<?= $escaper->escapeHtml($block->escapeHtml($shipRate->getMinAmount() !== null ? $shipRate->getMinAmount() : '1.00')) ?>"
                        placeholder="<?= $escaper->escapeHtml(__('e.g., 50.00')) ?>">
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!$isThreshold): ?>
                <div class="field required">
                    <label for="service_type" class="label">
                        <span><?= $escaper->escapeHtml(__('Service type')); ?></span>
                    </label>
                    <div class="tooltip">
                        <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                        <span class="tooltiptext"><?=__('Service types are defined by marketplace administrators') ?></span>
                    </div>
                    <div class="control">
                        <select data-validate="{'validate-select':true}"
                                title="<?= $escaper->escapeHtml(__('Service type')); ?>"
                                id="service_type" name="service_type" class="required-entry" data-ui-id="select-service-type">
                            <?php foreach ($serviceTypeOptions as $serviceTypeOption){ ?>
                                <?php if ($shipRate->getServiceType() === $serviceTypeOption['value']): ?>
                                    <option value="<?= $escaper->escapeHtml($serviceTypeOption['value']); ?>"
                                            selected><?= $escaper->escapeHtml(__($serviceTypeOption['label'])); ?>
                                    </option>
                                <?php else: ?>
                                    <option
                                        value="<?= $escaper->escapeHtml($serviceTypeOption['value']); ?>">
                                        <?= $escaper->escapeHtml(__($serviceTypeOption['label'])); ?>
                                    </option>
                                <?php endif; ?>
                            <?php } ?>
                        </select>
                    </div>
                </div>
                <?php endif; ?>

                <div class="field required">
                    <label class="label" for="countries">
                        <span><?php /* @escapeNotVerified */ echo __('Countries') ?></span>
                    </label>
                    <div class="tooltip">
                    <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select countries')) ?></span>
                    </div>
                    <div class="control">
                        <?php echo $countriesListHtml; ?>
                    </div>
                </div>

                <?php if (!$isThreshold): ?>
                <div class="field">
                    <label for="return_address_id" class="label">
                        <span><?= $escaper->escapeHtml(__('Return Address')); ?></span>
                    </label>
                    <div class="tooltip">
                        <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                        <span class="tooltiptext"><?=__('Select which of your return addresses should be used for this method') ?></span>
                    </div>
                    <div class="control">
                        <select
                                title="<?= $escaper->escapeHtml(__('Return Address')); ?>"
                                id="return_address_id" name="return_address_id" class="required-entry" data-ui-id="select-region-covered">
                            <?php foreach ($returnAddressOptions as $returnAddressOption){ ?>
                                <?php if ($shipRate->getReturnAddressId() == $returnAddressOption['value']): ?>
                                    <option value="<?= $escaper->escapeHtml($returnAddressOption['value']); ?>"
                                            selected><?= $escaper->escapeHtml(__($returnAddressOption['label'])); ?>
                                    </option>
                                <?php else: ?>
                                    <option
                                        value="<?= $escaper->escapeHtml($returnAddressOption['value']); ?>">
                                        <?= $escaper->escapeHtml(__($returnAddressOption['label'])); ?>
                                    </option>
                                <?php endif; ?>
                            <?php } ?>
                        </select>
                    </div>
                </div>
                <div class="field required" style="width: 17%; float:left">
                    <label for="packing_time" class="label">
                        <span><?= $escaper->escapeHtml(__('Packing Time')); ?></span>
                    </label>
                    <div class="tooltip">
                        <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                        <span class="tooltiptext"><?= $escaper->escapeHtml(__('Estimated days required for order packing')) ?></span>
                    </div>
                    <div class="control">
                        <input type="text" class="input-text required-entry validate-digits"
                               name="packing_time"
                               data-validate="{required:true}" title="<?= $escaper->escapeHtml(__("days")); ?>"
                               id="packing_time" value="<?= $escaper->escapeHtml($block->escapeHtml($shipRate->getPackingTime())) ?>">
                    </div>
                </div>
                <div style="width: 8%; float: left; padding-top:14px">
                    <label style="font-size: 48px; ">&plus;</label>
                </div>
                <div class="field required" style="width: 17%; float:left">
                    <label for="delivery_time" class="label">
                        <span><?= $escaper->escapeHtml(__('Delivery Time')); ?></span>
                    </label>
                    <div class="tooltip">
                        <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                        <span class="tooltiptext"><?= $escaper->escapeHtml(__('Estimated days required for package delivery')) ?></span>
                    </div>
                    <div class="control">
                        <input type="text" class="input-text required-entry validate-digits"
                               name="delivery_time"
                               data-validate="{required:true}" title="<?= $escaper->escapeHtml(__("days")); ?>"
                               id="delivery_time" value="<?= $escaper->escapeHtml($block->escapeHtml($shipRate->getDeliveryTime())) ?>">
                    </div>
                </div>
                <div style="width: 8%; float: left; padding-top:14px">
                    <label style="font-size: 48px; ">&equals;</label>
                </div>
                <div class="field required" style="width: 17%; float:left">
                    <label for="total_lead_time" class="label">
                        <span><?= $escaper->escapeHtml(__('Total Lead Time')); ?></span>
                    </label>
                    <div class="tooltip">
                        <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                        <span class="tooltiptext"><?= $escaper->escapeHtml(__('Estimated days required for order to reach customer')) ?></span>
                    </div>
                    <div class="control">
                        <input type="text" class="input-text required-entry validate-digits validate-greater-than-zero"
                               name="total_lead_time"
                               data-validate="{required:true}"
                               title="<?= $escaper->escapeHtml(__("days")); ?>"
                               id="total_lead_time"
                               value="<?= $escaper->escapeHtml($block->escapeHtml($shipRate->getDeliveryTime())) ?>"
                               readonly="readonly"
                        />
                    </div>
                </div>
                <div class="field required">
                    <label for="weight" class="label">
                        <span><?= $escaper->escapeHtml(__("Weight (up to this weight, in %1)", $weightUnits)); ?></span>
                    </label>
                    <div class="tooltip">
                    <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Specify max weight for this method/price, in %1',$weightUnits)) ?></span>
                    </div>
                    <div class="control">
                        <input type="text" class="input-text required-entry validate-digits"
                        name="weight"
                        data-validate="{required:true}" title="<?= $escaper->escapeHtml(__("weight")); ?>"
                        id="weight" value="<?= $escaper->escapeHtml($block->escapeHtml($shipRate->getWeight())) ?>">
                    </div>
                </div>
                <div class="field required">
                    <label for="shipping_price" class="label">
                        <span><?= $escaper->escapeHtml(__("Shipping price (in USD)")); ?></span>
                    </label>
                    <div class="tooltip">
                     <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                     <span class="tooltiptext"><?= $escaper->escapeHtml(__('Update your shipping price in USD')) ?></span>
                     </div>
                    <div class="control">
                        <input type="text" class="input-text required-entry" name="shipping_price"
                        data-validate="{required:true}" title="<?= $escaper->escapeHtml(__("shipping price")); ?>"
                        id="shipping_price" value="<?= $escaper->escapeHtml($block->escapeHtml($shipRate->getShippingPrice())) ?>">
                    </div>
                </div>
                <?php endif; ?>

                <div class="field">
                    <label for="free_shipping" class="label">
                        <span><?= $escaper->escapeHtml(__("Free Shipping")); ?></span>
                    </label>
                    <div class="tooltip">
                        <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
                        <span class="tooltiptext"><?= $escaper->escapeHtml(__('Free Shipping')) ?></span>
                    </div>
                    <div class="control">
                        <input type="hidden" name="free_shipping" value="0">
                        <input type="checkbox" class="input-checkbox" id="free_shipping" name="free_shipping" style="width: 6%"
                               title="<?= $escaper->escapeHtml(__("free shipping")); ?>"
                               value="1" <?= $shipRate->getFreeShipping() ? 'checked="checked"' : '' ?>>
                    </div>
                </div>
            </div>
        </form>
    <?php else: ?>
        <h2 class="wk-mp-error-msg">
            <?= $escaper->escapeHtml(__("To Become Seller Please Contact to Admin.")); ?>
        </h2>
    <?php endif; ?>
</div>

<script>
    require(['jquery'], function($) {
        $(document).ready(function() {
            var back = "<?php echo $backUrl; ?>";
            $("#back").click(function(){
                //alert("hi");
                window.location.replace(back);
            });
        });
    });
</script>



<script type="text/javascript">
    require(['jquery', 'select2'], function ($) {
        $(document).ready(function () {
            $('.custom-multiselect').select2({
                placeholder: "Select countries",
                allowClear: true
            });
        });
    });

    require(['jquery'], function($) {
        $(document).ready(function() {
            function calculateTotalLeadTime() {
                var packingTime = parseInt($('#packing_time').val(), 10) || 0;
                var deliveryTime = parseInt($('#delivery_time').val(), 10) || 0;
                var totalLeadTime = packingTime + deliveryTime;

                $('#total_lead_time').val(totalLeadTime);
            }

            // Bind event listeners to both input fields
            $('#packing_time, #delivery_time').on('input', function() {
                calculateTotalLeadTime();
            });

            // Initial calculation in case the fields already have values
            calculateTotalLeadTime();
        });
    });
</script>

<style>
    .select2-search__field {
        height: auto !important;
    }
</style>


