<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <title>Manage Shipping Methods</title>
        <css src="Webkul_Marketplace::css/layout.css"/>
        <css src="Webkul_Marketplace::css/product.css"/>
    </head>
    <body>
        <referenceBlock name="seller.page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Manage Shipping Methods</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="seller.content">
            <block class="Magento\Framework\View\Element\Template" name="sellership_rate_manage_tabs" template="Coditron_CustomShippingRate::shiprate/tabs.phtml" cacheable="false">
                <block class="Magento\Framework\View\Element\Template" name="shipping_methods_tab" template="Coditron_CustomShippingRate::shiprate/list.phtml" cacheable="false"/>
                <block class="Magento\Framework\View\Element\Template" name="free_shipping_thresholds_tab" template="Coditron_CustomShippingRate::shiprate/thresholds.phtml" cacheable="false"/>
            </block>
        </referenceContainer>
        <referenceContainer name="shipping_methods_tab">
            <uiComponent name="sellership_rates_list_front"/>
        </referenceContainer>
        <referenceContainer name="free_shipping_thresholds_tab">
            <uiComponent name="sellership_thresholds_list_front"/>
        </referenceContainer>
    </body>
</page>
