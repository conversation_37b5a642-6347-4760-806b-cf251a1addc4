<?php

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

class Edit extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Seller Category Edit action.
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        if (!$this->_marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        $resultPage = $this->_resultPageFactory->create();
        if ($this->_marketplaceHelper->getIsSeparatePanel()) {
            $resultPage->addHandle('mpsellership_layout2_rate_edit');
        }

        $isThreshold = $this->getRequest()->getParam('threshold', false);

        if (!empty($this->getRequest()->getParam("shiptablerates_id"))) {
            $sellerShiprate = $this->getSellerShiprate();
            if (empty($sellerShiprate->getShiptableratesId())) {
                $this->messageManager->addError("Shipping method does not exist");
                $redirectPath = $isThreshold ? 'managewithTabs#free-shipping-thresholds' : 'managewithTabs#shipping-methods';
                return $this->resultRedirectFactory->create()->setPath(
                    'coditron_customshippingrate/shiptablerates/' . $redirectPath,
                    ['_secure' => $this->getRequest()->isSecure()]
                );
            }

            $title = $isThreshold ? 'Edit Free Shipping Threshold' : $sellerShiprate->getCourierName();
        } else {
            $title = $isThreshold ? "New Free Shipping Threshold" : "New Shipping Method";
        }

        $resultPage->getConfig()->getTitle()->set(__($title));
        return $resultPage;
    }
}
