<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Seller Category Save action
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $isThreshold = $this->getRequest()->getParam('threshold', false);

        if ($this->getRequest()->isPost()) {
            try {
                if (!$this->_formKeyValidator->validate($this->getRequest())) {
                    $redirectPath = $isThreshold ? 'managewithTabs#free-shipping-thresholds' : 'managewithTabs#shipping-methods';
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/' . $redirectPath,
                        ['_secure' => $this->getRequest()->isSecure()]
                    );
                }

                $postData = $this->getRequest()->getPostValue();
                $postData['seller_id'] = $this->getSellerId();

                // Handle threshold-specific logic
                if ($isThreshold) {
                    // For thresholds, ensure min_amount is set and > 0
                    if (!isset($postData['min_amount']) || (float)$postData['min_amount'] <= 0) {
                        $postData['min_amount'] = 1.00; // Default minimum amount
                    }
                    // Set default values for threshold records
                    if (!isset($postData['courier_name']) || empty($postData['courier_name'])) {
                        $postData['courier_name'] = 'Free Shipping Threshold';
                    }
                    if (!isset($postData['service_type']) || empty($postData['service_type'])) {
                        $postData['service_type'] = 'standard'; // Default service type
                    }
                    if (!isset($postData['weight']) || empty($postData['weight'])) {
                        $postData['weight'] = '999999'; // High weight limit for thresholds
                    }
                    if (!isset($postData['shipping_price']) || empty($postData['shipping_price'])) {
                        $postData['shipping_price'] = '0.00'; // Free shipping
                    }
                    // Set free shipping to true for thresholds
                    $postData['free_shipping'] = 1;
                } else {
                    // For regular shipping methods, ensure min_amount is null
                    $postData['min_amount'] = null;
                }

                $result = $this->_helper->validateData($postData);
                if ($result['error']) {
                    $this->messageManager->addError(__($result['msg']));
                    $redirectPath = $isThreshold ? 'managewithTabs#free-shipping-thresholds' : 'managewithTabs#shipping-methods';
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/' . $redirectPath,
                        ['_secure' => $this->getRequest()->isSecure()]
                    );
                }

                $sellerShiprate = $this->getSellerShiprate();


                if ($postData['id']) {
                    $sellerShiprate->addData($postData)->setShiptableratesId($postData['id']);
                } else {
                    $sellerShiprate->setData($postData);
                }

                $sellerShiprate->save();
                $id = $sellerShiprate->getShiptableratesId();
                $successMessage = $isThreshold ? "Free Shipping Threshold saved successfully." : "Shipping Rate saved successfully.";
                $this->messageManager->addSuccess(__($successMessage));
                $this->_helper->clearCache();

                $editParams = ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()];
                if ($isThreshold) {
                    $editParams['threshold'] = 1;
                }

                return $this->resultRedirectFactory->create()->setPath(
                    'coditron_customshippingrate/shiptablerates/edit',
                    $editParams
                );
            } catch (\Exception $e) {
                $this->messageManager->addError($e->getMessage());
                $redirectPath = $isThreshold ? 'managewithTabs#free-shipping-thresholds' : 'managewithTabs#shipping-methods';
                return $this->resultRedirectFactory->create()->setPath(
                    'coditron_customshippingrate/shiptablerates/' . $redirectPath,
                    ['_secure' => $this->getRequest()->isSecure()]
                );
            }
        } else {
            return $this->resultRedirectFactory->create()->setPath(
                'coditron_customshippingrate/shiptablerates/managewithTabs',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }
    }
}
